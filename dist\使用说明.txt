微信自动化添加好友程序 - 使用说明
========================================

📋 程序信息
- 程序名称：微信自动化添加好友.exe
- 版本：1.0.0
- 文件大小：115.7 MB
- 打包时间：2025年8月4日

🚀 快速开始
1. 双击"微信自动化添加好友.exe"启动程序
2. 程序会自动打开图形用户界面
3. 按照界面提示配置参数和选择Excel文件
4. 点击"开始自动化"按钮开始执行

📁 文件说明
- 微信自动化添加好友.exe：主程序文件
- 使用说明.txt：本说明文件

⚙️ 系统要求
- 操作系统：Windows 7/8/10/11 (64位)
- 内存：至少4GB RAM
- 硬盘空间：至少200MB可用空间
- 网络：需要网络连接
- 其他：需要安装Microsoft Visual C++ Redistributable

🔧 运行环境
- 本程序为独立可执行文件，无需安装Python环境
- 包含所有必要的依赖库和资源文件
- 可以在没有Python的Windows系统上直接运行

📝 使用步骤
1. 准备Excel文件（包含手机号码、姓名等信息）
2. 确保微信已经登录并打开
3. 启动本程序
4. 在界面中配置相关参数：
   - 选择Excel文件路径
   - 设置执行时间段
   - 配置其他选项
5. 点击"开始自动化"开始执行
6. 程序会自动处理微信好友添加流程
7. 查看日志和统计信息

⚠️ 注意事项
1. 使用前请确保微信已经正常登录
2. 建议在使用前备份重要数据
3. 程序运行期间请勿手动操作微信
4. 遵守微信使用规范，避免频繁操作
5. 如遇到问题，请查看程序日志文件

🐛 故障排除
1. 如果程序无法启动：
   - 检查是否安装了Visual C++ Redistributable
   - 尝试以管理员身份运行
   - 检查防病毒软件是否阻止了程序

2. 如果程序运行异常：
   - 检查Excel文件格式是否正确
   - 确保微信窗口可见且未最小化
   - 查看程序生成的日志文件

3. 如果自动化失败：
   - 检查网络连接
   - 确认微信版本兼容性
   - 调整程序执行参数

📞 技术支持
如需技术支持，请提供：
- 操作系统版本
- 程序版本信息
- 错误日志文件
- 问题详细描述

🔄 更新说明
- 本版本为独立打包版本
- 如需更新，请下载新版本的exe文件
- 配置文件和数据文件可以保留使用

📄 许可证
本程序仅供学习和研究使用，请遵守相关法律法规。

========================================
打包完成时间：2025年8月4日
程序版本：v1.0.0
========================================
